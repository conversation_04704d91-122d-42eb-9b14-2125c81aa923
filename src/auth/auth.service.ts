import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { UserStatus, Users } from '../models/entities/permissions/users.entity';
import { UsersDto } from '../models/dto/users.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, QueryRunner, Repository } from 'typeorm';
import { plainToClass } from 'class-transformer';
import { ProfilesDto } from '../models/dto/profiles.dto';
import { GroupsDto } from '../models/dto/groups.dto';
import { BaseService } from '../core/base-service/base.service';
import { PermissionsService } from './permissions/permissions.service';
import { ProfileResources, Profiles } from '../models/entities/permissions/profiles.entity';
import {
  Groups,
  GroupSitesStudies,
  GroupsStudies,
  GroupTypes,
} from '../models/entities/permissions/groups.entity';
import { Permissions } from '../models/entities/permissions/permissions.entity';
import { Inject<PERSON>inoLogger, PinoLogger } from 'nestjs-pino';
import { ClerkService } from 'src/core/clerk/clerk.services';
import { ClerkPublicMetadataDto } from '../models/dto/clerk/clerk.dto';
import { ProfilesService } from '../profiles/profiles.service';
import { Roles, RolesProfiles } from 'src/models/entities/permissions/roles.entity';
import { Studies } from 'src/models/entities/studies.entity';

export enum RolesEnum {
  CLINCOVE_ADMIN = 'Clincove Admin',
  SITE_USER = 'Site User',
  CRA_USER = 'CRA User',
}

@Injectable()
export class AuthService extends BaseService {
  constructor(
    @InjectRepository(Users)
    private usersRepository: Repository<Users>,
    @InjectRepository(GroupSitesStudies)
    private groupSitesStudiesRepository: Repository<GroupSitesStudies>,
    protected dataSource: DataSource,
    private permissionsService: PermissionsService,
    private clerkService: ClerkService,
    private profilesService: ProfilesService,
    @InjectPinoLogger(AuthService.name) protected readonly logger: PinoLogger,
  ) {
    super(dataSource, logger);
  }

  public async createUserFromClerkUser(currentUserId: string) {
    const clerkUser = await this.clerkService.getUserWithMetadata(currentUserId);
    if (clerkUser.publicMetadata.region !== process.env.COUNTRY) {
      this.logger.error('Invalid region', clerkUser.publicMetadata.region, process.env.COUNTRY);
    }

    const user = await this.createUser(currentUserId, clerkUser.publicMetadata);
    return user;
  }

  private async createUser(userId: string, metadata: ClerkPublicMetadataDto) {
    return this.executeInTransaction(async (queryRunner) => {
      const roleToAssign = await queryRunner.manager.findOne(Roles, {
        where: { id: metadata.roleId },
      });
      if (!roleToAssign) {
        throw new NotFoundException(`Role with id ${metadata.roleId} not found`);
      }
      const user = new Users();
      user.id = userId;
      user.firstName = metadata.firstName;
      user.lastName = metadata.lastName;
      user.email = metadata.email;
      user.phone = metadata.phone;
      user.isActive = true;
      user.status = UserStatus.ACTIVE;
      user.lastLogin = null;
      user.loginAttempts = 0;
      user.loginCount = 0;
      user.currentSignInIp = null;

      await queryRunner.manager.save(Users, user);
      const userProfile = await this.profilesService.createUserProfile(
        user.id,
        metadata.groupId,
        roleToAssign.id,
        queryRunner,
      );

      // --- *** START: Inserted Study Comparison Logic *** ---
      const group = await queryRunner.manager.findOneOrFail(Groups, {
        where: { id: metadata.groupId },
        relations: ['site'],
      });
      const siteId = group.site?.id;
      if (!siteId && group.type === GroupTypes.SITE) {
        throw new InternalServerErrorException(
          `Configuration error: Group ${group.id} type SITE missing siteId.`,
        );
      }
      const defaultStudyIds = (
        await this._getDefaultStudyIdsForGroup(group.id, queryRunner)
      ).sort();

      const providedStudyIds = Array.isArray(metadata.studyIds)
        ? [...new Set(metadata.studyIds)].sort()
        : [];

      const doListsMatch =
        providedStudyIds.length === defaultStudyIds.length &&
        providedStudyIds.every((id, index) => id === defaultStudyIds[index]);

      let profilePermissionsChanged = false; // Track if we need to save profile again
      if (providedStudyIds.length === 0 || doListsMatch) {
        if (userProfile.hasCustomPermissions !== false) {
          userProfile.hasCustomPermissions = false;
          profilePermissionsChanged = true;
          this.logger.info({ profileId: userProfile.id }, 'Setting hasCustomPermissions=false.');

          await queryRunner.manager.delete(ProfileResources, { profileId: userProfile.id });
        }
      } else {
        if (userProfile.hasCustomPermissions !== true) {
          userProfile.hasCustomPermissions = true;
          profilePermissionsChanged = true;
        }

        await queryRunner.manager.delete(ProfileResources, { profileId: userProfile.id });

        const validStudyIds = await queryRunner.manager
          .find(Studies, { where: { id: In(providedStudyIds) }, select: ['id'] })
          .then((s) => s.map((x) => x.id));

        // Create new resource links
        const resourcesToCreate = validStudyIds.map((studyId) => ({
          profileId: userProfile.id,
          studyId: studyId,
          siteId: siteId, // Use the group's siteId
        }));
        if (resourcesToCreate.length > 0) {
          await queryRunner.manager.save(ProfileResources, resourcesToCreate);
          this.logger.info(
            { profileId: userProfile.id, count: resourcesToCreate.length },
            'Populated profile_resources.',
          );
        }
      }
      // Save the Profile *only if* the hasCustomPermissions flag changed
      if (profilePermissionsChanged) {
        await queryRunner.manager.save(Profiles, userProfile);
      }
      // --- *** END: Inserted Study Comparison Logic *** ---

      user.currentProfile = userProfile;
      user.currentProfile.currentGroup = userProfile.currentGroup;
      await queryRunner.manager.save(Users, user);
      await this.clerkService.removeUserPublicMetadata(userId);

      // Load the missing relationships
      const rolesProfiles = await queryRunner.manager.find(RolesProfiles, {
        where: { profileId: userProfile.id },
        relations: ['role'],
      });
      userProfile.rolesProfiles = rolesProfiles;

      const groupWithSite = await queryRunner.manager.findOne(Groups, {
        where: { id: userProfile.currentGroup.id },
        relations: ['site'],
      });
      userProfile.currentGroup = groupWithSite;
      return user;
    }, 'Failed to create user');
  }
  private async _getDefaultStudyIdsForGroup(
    groupId: string,
    queryRunner: QueryRunner,
  ): Promise<string[]> {
    this.logger.debug({ groupId }, 'AuthService: Fetching default study IDs for group');
    try {
      const groupStudiesLinks = await queryRunner.manager.find(GroupsStudies, {
        where: { groupId: groupId },
        select: ['studyId'],
      });
      const studyIds = groupStudiesLinks.map((link) => link.studyId);
      return studyIds;
    } catch (error) {
      this.logger.error(
        error,
        `AuthService: Failed to fetch default study IDs for group ${groupId}`,
      );
      throw error; // Re-throw
    }
  }

  async getIsAuthenticated(currentUserId: string) {
    try {
      let user = await this.usersRepository.findOne({
        where: { id: currentUserId, isActive: true },
        relations: [
          'currentProfile',
          'currentProfile.currentGroup',
          'currentProfile.rolesProfiles',
          'currentProfile.rolesProfiles.role',
          'currentProfile.currentGroup.site',
          'currentProfile.currentGroup.entitlements',
        ],
      });

      if (!user) {
        this.logger.info('User not found, creating user from clerk user');
        user = await this.createUserFromClerkUser(currentUserId);
      }

      const roles = user.currentProfile.rolesProfiles.map((role) => {
        return {
          name: role.role.name,
          id: role.role.id,
        };
      });
      const permissions = await this.findAllPermissionsOfUser(user);

      return {
        authenticated: true,
        userId: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        status: user.status,
        userSettings: user.userSettings,
        currentProfile: {
          id: user.currentProfile.id,
          name: user.currentProfile.name,
          type: user.currentProfile.currentGroup.type,
          currentGroup: {
            id: user.currentProfile.currentGroup.id,
            name: user.currentProfile.currentGroup.name,
            type: user.currentProfile.currentGroup.type,
            phone: user.currentProfile.currentGroup.phone,
            email: user.currentProfile.currentGroup.email,
            groupInstitutionType: user.currentProfile.currentGroup.groupInstitutionType,
            siteId: user.currentProfile.currentGroup.site?.id || undefined,
            entitlements: user.currentProfile.currentGroup.entitlements,
            site: user.currentProfile?.currentGroup?.site || undefined,
          },
          shortId: user.currentProfile.shortId,
        },
        roles: roles,
        permissions: permissions,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async findByPayload(payload: any): Promise<Users> {
    if (!payload.sub) throw new HttpException('Sub is undefined in token', HttpStatus.UNAUTHORIZED);

    return await this.usersRepository.findOne({
      where: { id: payload.sub },
      relations: ['currentProfile', 'currentProfile.currentGroup'],
    });
  }

  async getUserById(userId: string): Promise<Users> {
    if (!userId) return undefined;

    const user = await this.usersRepository.findOneOrFail({
      where: { id: userId },
      relations: ['currentProfile', 'currentProfile.currentGroup'],
    });

    return user;
  }

  async getActiveUserById(userId: string): Promise<Users> {
    if (!userId) return undefined;

    const user = await this.usersRepository.findOneOrFail({
      where: { id: userId, isActive: true },
      relations: [
        'currentProfile',
        'currentProfile.currentGroup',
        'currentProfile.currentGroup.site',
      ],
    });

    return user;
  }

  async findAllPermissionsOfUser(user: any): Promise<Permissions[]> {
    const currentUser = await this.getUserById(user.id);
    const permissions: Permissions[] = await this.permissionsService.getPermissionsForProfile(
      currentUser.currentProfile.id,
    );

    return permissions;
  }

  // // Todo: Move to scope-access service
  // async getStudiesForProfile(profile: Profiles): Promise<string[]> {
  //   const studies: string[] = [];

  //   const groupSitesStudies = await this.groupSitesStudiesRepository.find({
  //     where: { groupId: profile.currentGroup.id },
  //   });

  //   return studies;
  // }

  // Todo: Move this to a mapper service
  async mapUserToUserDto(user: Users): Promise<UsersDto> {
    const userDto = plainToClass(UsersDto, user, { excludeExtraneousValues: true });
    const profileDto = plainToClass(ProfilesDto, user.currentProfile, {
      excludeExtraneousValues: true,
    });
    const groupDto = plainToClass(GroupsDto, user.currentProfile.currentGroup, {
      excludeExtraneousValues: true,
    });
    userDto.currentProfile = profileDto;
    userDto.currentProfile.currentGroup = groupDto;
    return userDto;
  }
}
